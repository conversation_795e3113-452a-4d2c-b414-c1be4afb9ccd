{"actions.apply": "", "actions.cancel": "", "actions.confirm": "", "actions.delete": "", "actions.edit": "", "actions.pause": "", "actions.reject": "", "actions.resend": "", "actions.save": "", "actions.saved": "", "actions.send": "", "actions.sent": "", "actions.signIn": "", "actions.signOut": "", "actions.signUp": "", "actions.submit": "", "mock.message": "", "nouns.confirmation": "", "nouns.description": "", "nouns.email": "", "nouns.error": "", "nouns.from": "", "nouns.name": "", "nouns.password": "", "nouns.price": "", "nouns.service": "", "errors.auth.unauthenticated": "", "errors.auth.permissionDenied": "", "errors.auth.permissionDeniedWithOperation": "", "errors.auth.adminOnly": "", "errors.auth.userNotFound": "", "errors.auth.tonWalletRequired": "", "errors.validation.requiredField": "", "errors.validation.positiveAmountRequired": "", "errors.validation.invalidOrderId": "", "errors.validation.invalidCollectionId": "", "errors.validation.invalidPrice": "", "errors.validation.invalidSecondaryMarketPrice": "", "errors.validation.invalidBotToken": "", "errors.validation.botTokenRequired": "", "errors.validation.ownedGiftIdRequired": "", "errors.validation.userIdOrTgIdRequired": "", "errors.order.orderNotFound": "", "errors.order.insufficientBalance": "", "errors.order.collectionNotFound": "", "errors.order.collectionNotActive": "", "errors.order.onlyPaidOrdersSecondaryMarket": "", "errors.order.onlyBuyerCanSetSecondaryPrice": "", "errors.order.orderMustHaveBuyerAndSeller": "", "errors.order.secondaryPriceExceedsCollateral": "", "errors.order.orderNotAvailableSecondaryMarket": "", "errors.order.onlyPaidOrdersPurchasable": "", "errors.order.sellerCannotPurchaseOwnOrder": "", "errors.order.buyerCannotPurchaseSameOrder": "", "errors.order.secondaryPriceBelowMinimum": "", "errors.order.orderMustBePaidStatus": "", "errors.order.orderMustBeGiftSentStatus": "", "errors.withdrawal.amountBelowMinimum": "", "errors.withdrawal.amountAboveMaximum": "", "errors.withdrawal.insufficientAvailableBalance": "", "errors.withdrawal.amountTooSmallAfterFees": "", "errors.telegram.initDataRequired": "", "errors.telegram.botTokenNotConfigured": "", "errors.telegram.invalidTelegramData": "", "errors.telegram.firebaseAuthError": "", "errors.telegram.iamPermissionError": "", "errors.generic.serverError": "", "errors.generic.unknownError": "", "errors.generic.operationFailed": "", "errors.generic.authenticationFailed": ""}